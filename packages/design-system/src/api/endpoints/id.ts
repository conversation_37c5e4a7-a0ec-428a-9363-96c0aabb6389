import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsID: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-indo/get-asker-env',
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-indo/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-indo/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-indo/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-indo/get-up-coming-tasks',
  },
  [EndpointKeys.getScheduleTasks]: {
    path: 'v5/api-asker-indo/get-schedule-tasks',
  },
  [EndpointKeys.getMonthlyTasks]: {
    path: 'v5/api-asker-indo/get-subscription-by-userId',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-indo/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking-indo/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking-indo/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking-indo/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking-indo/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking-indo/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-indo/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskSubscriptionCleaning]: {
    path: 'v5/booking-indo/subscription',
  },
  [EndpointKeys.postTaskOfficeCleaningSubscription]: {
    path: 'v5/booking-indo/subscription-office-cleaning',
  },
  [EndpointKeys.getFinancialAccount]: {
    path: 'v5/api-asker-indo/get-financial-account',
  },
  [EndpointKeys.bookTaskForceTasker]: {
    path: 'v5/booking-indo/book-task-force-tasker',
  },
  [EndpointKeys.postTaskCleaning]: {
    path: 'v5/booking-indo/home-cleaning',
  },
  [EndpointKeys.getOutstandingPayment]: {
    path: 'v5/api-asker-indo/get-outstanding-payment',
  },
  [EndpointKeys.getTaskDetail]: {
    path: 'v5/api-asker-indo/get-task-detail',
  },
  [EndpointKeys.cancelTask]: {
    path: 'v5/cancel-task-indo/cancel',
  },
  [EndpointKeys.addFavoriteService]: {
    path: 'v5/api-asker-indo/add-favourite-services',
  },
  [EndpointKeys.postTaskWaterHeater]: {
    path: 'v5/booking-indo/water-heater',
  },
  [EndpointKeys.postTaskMassage]: {
    path: 'v5/booking-indo/massage',
  },
  // Housekeeping
  [EndpointKeys.createHousekeepingLocation]: {
    path: 'v5/api-asker-indo/add-housekeeping-location',
  },
  [EndpointKeys.updateRoomHousekeepingLocation]: {
    path: 'v5/api-asker-indo/update-housekeeping-location',
  },
  [EndpointKeys.deleteRoomHousekeepingLocation]: {
    path: 'v5/api-asker-indo/delete-housekeeping-location',
  },
  // Update task
  [EndpointKeys.changePremiumOption]: {
    path: 'v5/update-task-indo/update-task-premium',
  },
  [EndpointKeys.updateTaskNote]: {
    path: 'v5/update-task-indo/update-task-note',
  },
  [EndpointKeys.getHistoryTasks]: {
    path: 'v5/api-asker-indo/get-list-history-tasks',
  },
  [EndpointKeys.getExtraMoneyUpdateDateTime]: {
    path: 'v5/update-task-indo/get-extra-money-update-date-time',
  },
  [EndpointKeys.createUpdateDateTimeRequest]: {
    path: 'v5/update-task-indo/create-update-date-time-request',
  },
  [EndpointKeys.askerCreateRequestUpdateDetailV4]: {
    path: 'v5/update-task-indo/asker-create-request',
  },
  [EndpointKeys.updateTaskHomeCleaning]: {
    path: 'v5/update-task-indo/home-cleaning',
  },
  [EndpointKeys.updateTaskAirConditioner]: {
    path: 'v5/update-task-indo/air-conditioner',
  },
  [EndpointKeys.updateTaskDeepCleaning]: {
    path: 'v5/update-task-indo/deep-cleaning',
  },
  [EndpointKeys.updateTaskChildCare]: {
    path: 'v5/update-task-indo/child-care',
  },
  [EndpointKeys.updateTaskHomeCooking]: {
    path: 'v5/update-task-indo/home-cooking',
  },
  [EndpointKeys.updateTaskDisinfection]: {
    path: 'v5/update-task-indo/disinfection',
  },
  [EndpointKeys.updateTaskPatientCare]: {
    path: 'v5/update-task-indo/patient-care',
  },
  [EndpointKeys.updateTaskElderlyCare]: {
    path: 'v5/update-task-indo/elderly-care',
  },
  [EndpointKeys.updateTaskSofa]: {
    path: 'v5/update-task-indo/sofa',
  },
  [EndpointKeys.updateTaskOfficeCleaning]: {
    path: 'v5/update-task-indo/office-cleaning',
  },
  [EndpointKeys.updateTaskWashingMachine]: {
    path: 'v5/update-task-indo/washing-machine',
  },
  [EndpointKeys.updateTaskWaterHeater]: {
    path: 'v5/update-task-indo/water-heater',
  },
  [EndpointKeys.updateTaskOfficeCarpetCleaning]: {
    path: 'v5/update-task-indo/office-carpet-cleaning',
  },
  [EndpointKeys.updateTaskMassage]: {
    path: 'v5/update-task-indo/massage',
  },
  [EndpointKeys.updateTaskIndustrialCleaning]: {
    path: 'v5/update-task-indo/industrial-cleaning',
  },
  [EndpointKeys.updateTaskHomeMoving]: {
    path: 'v5/update-task-indo/home-moving',
  },
  [EndpointKeys.updateHouseKeeping]: {
    path: 'v5/update-task-indo/house-keeping',
  },
  [EndpointKeys.updateTaskBeautyCare]: {
    path: 'v5/update-task-indo/beauty-care',
  },
  [EndpointKeys.updateTaskIroning]: {
    path: 'v5/update-task-indo/ironing',
  },
  [EndpointKeys.updateTaskHairStyling]: {
    path: 'v5/update-task-indo/hair-styling',
  },
  [EndpointKeys.updateTaskMakeup]: {
    path: 'v5/update-task-indo/makeup',
  },
  [EndpointKeys.updateTaskNail]: {
    path: 'v5/update-task-indo/nail',
  },
  [EndpointKeys.updateGroceryAssistant]: {
    path: 'v5/update-task-indo/grocery-assistant',
  },
  [EndpointKeys.checkTaskerConflictUpdateTime]: {
    path: 'v5/update-task-indo/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskWashingMachine]: {
    path: 'v5/booking-indo/washing-machine',
  },
  [EndpointKeys.postTaskDisinfection]: {
    path: 'v5/booking-indo/disinfection',
  },
  [EndpointKeys.postTaskHomeCooking]: {
    path: 'v5/booking-indo/home-cooking',
  },
  [EndpointKeys.postTaskOfficeCarpetCleaning]: {
    path: 'v5/booking-indo/carpet-cleaning',
  },
  [EndpointKeys.postTaskIndustrialCleaning]: {
    path: 'v5/booking-indo/industrial-cleaning',
  },
  [EndpointKeys.postTaskSofaCleaning]: {
    path: 'v5/booking-indo/sofa',
  },
  [EndpointKeys.postTaskIroning]: {
    path: 'v5/booking-indo/ironing',
  },
  [EndpointKeys.postTaskLaundry]: {
    path: 'v5/booking-indo/laundry',
  },
  [EndpointKeys.postTaskHomeMoving]: {
    path: 'v5/booking-indo/home-moving',
  },

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-indo/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-indo/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-indo/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-indo/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-indo/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-indo/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-indo/subscription',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaning]: {
    path: 'v5/pricing-indo/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCareSubscription]: {
    path: 'v5/pricing-indo/subscription-child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaningSubscription]: {
    path: 'v5/pricing-indo/subscription-office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCareSubscription]: {
    path: 'v5/pricing-indo/subscription-elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCareSubscription]: {
    path: 'v5/pricing-indo/subscription-patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWaterHeater]: {
    path: 'v5/pricing-indo/water-heater',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWashingMachine]: {
    path: 'v5/pricing-indo/washing-machine',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDisinfection]: {
    path: 'v5/pricing-indo/disinfection',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeCooking]: {
    path: 'v5/pricing-indo/home-cooking',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCarpetCleaning]: {
    path: 'v5/pricing-indo/carpet-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIndustrialCleaning]: {
    path: 'v5/pricing-indo/industrial-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceSofaCleaning]: {
    path: 'v5/pricing-indo/sofa',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceMassage]: {
    path: 'v5/pricing-indo/massage',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIroning]: {
    path: 'v5/pricing-indo/ironing',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceLaundry]: {
    path: 'v5/pricing-indo/laundry',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeMoving]: {
    path: 'v5/pricing-indo/home-moving',
    isDualAuth: true,
  },
};
