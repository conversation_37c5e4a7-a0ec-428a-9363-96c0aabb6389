import { IHostelType, IRoomType, IHouseKeepingOption, SERVICES } from '@btaskee/design-system';
import { usePostTaskStore } from '@stores';

/**
 * Hook for tracking housekeeping-related events
 * This is a simplified version of the tracking hook from the old codebase
 */
export const useTrackingHousekeeping = () => {
  const { homeType, rooms, options, roomNumber } = usePostTaskStore();

  /**
   * Track step 2 housekeeping events
   * @param action The tracking action
   */
  const trackingStep2HouseKeeping = (action: string) => {
    // In the new architecture, we would implement actual tracking here
    console.log('Tracking housekeeping step 2:', {
      action,
      additionalInfo: {
        serviceType: homeType?.name,
        roomNumber: roomNumber,
        rooms: rooms.map((item: IRoomType) => {
          return { name: item?.name, quantity: item?.quantity };
        }),
        options: options.map((item: IHouseKeepingOption) => {
          return { name: item?.name };
        }),
      },
    });
  };

  /**
   * Track hostel type change events
   * @param param0 Object containing action and additionalInfo
   */
  const trackingClickChangeHostelType = ({
    action,
    additionalInfo,
  }: {
    action: string;
    additionalInfo: any;
  }) => {
    // In the new architecture, we would implement actual tracking here
    console.log('Tracking hostel type change:', {
      action,
      additionalInfo,
    });
  };

  return {
    trackingStep2HouseKeeping,
    trackingClickChangeHostelType,
  };
};
