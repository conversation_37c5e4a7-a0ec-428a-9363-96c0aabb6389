import React, { useMemo } from 'react';
import {
  <PERSON>ert,
  AnimationHelpers,
  BlockView,
  ColorsV2,
  CText,
  IconAssets,
  IHostelType,
  KeyboardAware,
  PrimaryButton,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { HostelType, RenderAddress, RoomType } from '@components';
import { useAppNavigation, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const ChooseService = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();

  const {
    hostelPlace,
    homeType,
    rooms,
    options,
    roomNumber,
    setHomeType,
    resetHousekeepingState,
  } = usePostTaskStore();

  const listHostelType = useMemo(() => {
    // For now, return empty array - this will need to be updated based on actual service structure
    return [];
  }, []);

  const handleChange = (type: IHostelType) => {
    AnimationHelpers.runLayoutAnimation();
    resetHousekeepingState();
    setHomeType(type);
  };

  const onChangeHostelType = (type: IHostelType) => {
    // If no home type selected yet
    if (isEmpty(homeType)) {
      AnimationHelpers.runLayoutAnimation();
      setHomeType(type);
      return;
    }

    // If selecting the same type again, do nothing
    if (homeType?.name === type?.name) {
      return;
    }

    // If home type selected but no rooms/options/room number, just change
    if (isEmpty(rooms) && isEmpty(options) && !roomNumber) {
      handleChange(type);
      return;
    }

    // If home type selected and has data, show confirmation dialog
    if (Alert.alert) {
      Alert.alert.open({
        title: t('HOUSE_KEEPING.CHANGE_TYPE_HOME'),
        message: [
          t('HOUSE_KEEPING.DESCRIPTION_CHANGE_TYPE_HOME'),
          t('HOUSE_KEEPING.CONFIRMED_CHANGE'),
        ],
        actions: [
          {
            text: t('BTN_BACK'),
            style: 'cancel',
          },
          {
            text: t('CONTINUE'),
            onPress: () => handleChange(type),
          },
        ],
      });
    }
  };

  const onConfirmed = async () => {
    navigation.navigate(RouteName.ChooseDateTime);
  };

  if (isEmpty(listHostelType)) {
    return (
      <BlockView style={styles.container}>
        <CText
          center
          margin={20}
        >
          {t('NOT_SUPPORT_CITY')}
        </CText>
      </BlockView>
    );
  }

  return (
    <BlockView style={styles.container}>
      <KeyboardAware contentContainerStyle={styles.contentContainerStyle}>
        <RenderAddress
          name={hostelPlace?.address}
          description={hostelPlace?.description}
          isShowFooter={true}
          icon={IconAssets.icLocation}
          descriptionStyle={{ color: ColorsV2.neutral600 }}
        />
        <HostelType
          onChangeHostelType={onChangeHostelType}
          typeHostelSelected={homeType}
          hostelTypes={listHostelType}
        />
        <RoomType hostelType={homeType} />
      </KeyboardAware>
      <PrimaryButton
        testID="btnNextStep2"
        title={t('CONTINUE')}
        onPress={onConfirmed}
      />
    </BlockView>
  );
};
