/**
 * ChooseHostel Screen
 *
 * Displays a list of locations for the user to choose from when booking a cleaning service.
 * Allows users to select an address which will be used for the cleaning service.
 */
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { Animated, ImageProps, ListRenderItem } from 'react-native';
import {
  Alert,
  AnimationHelpers,
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FlatList,
  HitSlop,
  IAddress,
  IconAssets,
  IconImage,
  LocationItem,
  NavigationService,
  PrimaryButton,
  RouteName as DesignSystemRouteName,
  SizedBox,
  Spacing,
  TouchableOpacity,
  useUserStore,
} from '@btaskee/design-system';
import { RouteProp, useRoute } from '@react-navigation/native';
import { debounce } from 'lodash-es';

import { useAppNavigation, useI18n, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { RootStackParamList } from '@navigation/types';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

const OptionItem = ({
  testID,
  title,
  onPress,
  icon,
}: {
  testID?: string;
  title: string;
  onPress: () => void;
  icon: ImageProps['source'];
}) => (
  <TouchableOpacity
    testID={testID}
    onPress={onPress}
  >
    <BlockView
      row
      center
      radius={BorderRadius.RADIUS_08}
    >
      <BlockView
        flex
        row
        jBetween
        horizontal
      >
        <BlockView
          padding={Spacing.SPACE_12}
          radius={BorderRadius.RADIUS_FULL}
          backgroundColor={ColorsV2.neutral100}
          center
        >
          <IconImage
            source={icon}
            color={ColorsV2.neutral900}
            size={16}
          />
        </BlockView>
        <CText
          flex
          margin={{ left: Spacing.SPACE_12 }}
        >
          {title}
        </CText>
      </BlockView>
      <IconImage
        source={IconAssets.icNext}
        color={ColorsV2.neutral600}
        size={20}
      />
    </BlockView>
  </TouchableOpacity>
);

type ChooseAddressRouteProp = RouteProp<
  RootStackParamList,
  RouteName.ChooseHostel
>;

/**
 * ChooseHostel component that displays a list of locations for the user to select
 *
 * @param navigation - Navigation prop for navigating between screens
 */
export const ChooseHostel = memo(() => {
  const route = useRoute<ChooseAddressRouteProp>();
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { user, getUser } = useUserStore();
  const { address, setAddress, resetHousekeepingState, setHostelPlace } =
    usePostTaskStore();
  const { onDeleteRoomHousekeepingLocation } = usePostTask();

  const isHideCurrentLocation = route?.params?.isHideCurrentLocation;

  // Animation values for each item
  const fadeAnims = useRef<{ [key: string]: Animated.Value }>({}).current;

  const locations = useMemo(() => {
    if (isHideCurrentLocation) {
      return (
        user?.housekeepingLocations?.filter(
          (item) => item._id !== address?._id,
        ) || []
      );
    }
    return user?.housekeepingLocations || [];
  }, [isHideCurrentLocation, user?.housekeepingLocations, address?._id]);

  // Apply layout animation when component mounts
  useEffect(() => {
    AnimationHelpers.runLayoutAnimation('easeInEaseOut', 500);
  }, []);

  /**
   * Handles the selection of an address and navigates to the next screen
   *
   * @param selectedAddress - The location selected by the user
   */
  const onChooseAddress = useCallback(
    async (selectedAddress: IAddress) => {
      if (!selectedAddress) return;
      if (address?._id !== selectedAddress._id) {
        resetHousekeepingState();
      }

      setAddress(selectedAddress);
      navigation.navigate(RouteName.ChooseService);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [address?._id, navigation],
  );

  /**
   * Creates or retrieves an animation value for a specific item
   *
   * @param index - The index of the item
   * @returns The animation value for the item
   */
  const getAnimationValue = useCallback(
    (index: number): Animated.Value => {
      const key = index.toString();
      if (!fadeAnims[key]) {
        fadeAnims[key] = new Animated.Value(0);
        // Start the animation with a staggered delay based on index
        Animated.timing(fadeAnims[key], {
          toValue: 1,
          duration: 400,
          delay: index * 100, // Stagger the animations
          useNativeDriver: true,
        }).start();
      }
      return fadeAnims[key];
    },
    [fadeAnims],
  );

  const _editRoom = (data: IAddress) => {
    // trackingServiceClick({
    //   screenName: TrackingScreenNames.ListOfPlaces,
    //   serviceName: SERVICES.HOUSE_KEEPING,
    //   action: TRACKING_ACTION.EditAddress,
    // });
    Alert.alert.close();
    setHostelPlace(data);
    navigation.navigate(RouteName.CreateNewHostel, { isEditRoom: true });
  };

  const _onDeleteLocation = debounce(async (data: IAddress) => {
    onDeleteRoomHousekeepingLocation(
      {
        locationId: data?._id || '',
      },
      {
        onSuccess: getUser,
      },
    );
  }, 300);

  const _handleDelete = debounce((data: IAddress) => {
    Alert.alert.open({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('CONFIRM_DELETE'),
      actions: [
        {
          text: t('BTN_BACK'),
          style: 'cancel',
        },
        {
          text: t('CONFIRM'),
          onPress: async () => {
            Alert.alert.close();
            _onDeleteLocation(data);
          },
        },
      ],
    });
  }, 300);

  const handleOptions = (data: IAddress) => {
    Alert.alert.open({
      title: t('OPTIONAL'),
      message: (
        <BlockView>
          <OptionItem
            testID="editLocationHousekeeping"
            title={t('UPDATE_LOCATION')}
            icon={IconAssets.icChange}
            onPress={() => _editRoom(data)}
          />
          <SizedBox
            height={1}
            color={ColorsV2.neutral100}
            margin={{ vertical: Spacing.SPACE_16 }}
          />
          <OptionItem
            testID="deleteLocationHousekeeping"
            title={t('DELETE_LOCATION')}
            icon={IconAssets.icTrash}
            onPress={() => {
              Alert.alert.close();
              _handleDelete(data);
            }}
          />
        </BlockView>
      ),
    });
  };

  /**
   * Renders a location item in the FlatList with animation
   */
  const renderItem: ListRenderItem<IAddress> = useCallback(
    ({ item, index }) => {
      const fadeAnim = getAnimationValue(index);

      return (
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [
              {
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0], // Slide up from 20px below
                }),
              },
            ],
          }}
        >
          <LocationItem
            testIDs={{
              item: `address${index + 1}`,
            }}
            isHideIcon
            shortAddress={item?.locationName || ''}
            address={item?.address || ''}
            onPress={() => onChooseAddress(item)}
            iconLeft={
              <TouchableOpacity
                testID={`btnEditLocation_${item?.shortAddress}`}
                onPress={() => handleOptions(item)}
                hitSlop={HitSlop.MEDIUM}
              >
                <IconImage
                  source={IconAssets.icOptions}
                  color={ColorsV2.neutral600}
                  size={20}
                />
              </TouchableOpacity>
            }
          />
        </Animated.View>
      );
    },
    [onChooseAddress, getAnimationValue],
  );

  /**
   * Renders a separator between list items
   */
  const itemSeparatorComponent = useCallback(
    () => <SizedBox height={16} />,
    [],
  );

  /**
   * Key extractor for the FlatList
   */
  const keyExtractor = useCallback(
    (_: IAddress, index: number) => index.toString(),
    [],
  );

  const onPressBtn = () => {
    if (user?._id) {
      navigation.navigate(RouteName.ChooseAddress);
    } else {
      NavigationService.navigate(DesignSystemRouteName.Auth, {
        isGoBack: true,
      });
    }
  };

  return (
    <BlockView style={styles.container}>
      <BlockView
        inset={['bottom']}
        style={styles.wrapFlatList}
      >
        <FlatList
          data={locations}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
          testID={'scrollChooseAddress'}
          ItemSeparatorComponent={itemSeparatorComponent}
          contentContainerStyle={styles.contentContainer}
        />
        <PrimaryButton
          testID="btnAddNewLocation"
          title={user?._id ? t('ADD_NEW_LOCATION') : t('SIGN_UP_NOW')}
          left={
            <BlockView margin={{ right: Spacing.SPACE_08 }}>
              <IconImage
                source={IconAssets.icPlus}
                color={ColorsV2.neutralWhite}
                size={20}
              />
            </BlockView>
          }
          onPress={onPressBtn}
        />
      </BlockView>
    </BlockView>
  );
});
