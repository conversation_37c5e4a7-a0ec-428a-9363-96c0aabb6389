import React, { useEffect, useMemo, useState } from 'react';
import {
  Alert,
  BlockView,
  ColorsV2,
  CTextInput,
  IconAssets,
  IconImage,
  KeyboardAware,
  PrimaryButton,
  Spacing,
  TouchableOpacity,
  useUserStore,
} from '@btaskee/design-system';
import { RouteProp, useRoute } from '@react-navigation/native';
import { debounce } from 'lodash-es';

import { RenderAddress } from '@components';
import { useAppNavigation, useI18n, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { RootStackParamList } from '@navigation/types';
import { usePostTaskStore } from '@stores';

import styles from './styles';

const RightIcon = ({ onPress }: { onPress?: () => void }) => {
  return (
    <TouchableOpacity
      testID="btnDelete"
      onPress={onPress}
      style={styles.icDelete}
    >
      <IconImage
        source={IconAssets.icTrash}
        color={ColorsV2.neutral400}
        size={24}
      />
    </TouchableOpacity>
  );
};

type ICreateNewHostelRoute = RouteProp<
  RootStackParamList,
  RouteName.CreateNewHostel
>;

export const CreateNewHostel = () => {
  const route = useRoute<ICreateNewHostelRoute>();
  const isEditRoom = route?.params?.isEditRoom;

  const { hostelPlace } = usePostTaskStore();

  const { t } = useI18n();
  const navigation = useAppNavigation();

  const { user, getUser } = useUserStore();
  const {
    onCreateHousekeepingLocation,
    onUpdateRoomHousekeepingLocation,
    onDeleteRoomHousekeepingLocation,
  } = usePostTask();

  const [locationId, setLocationId] = useState<string | undefined>();
  const [locationName, setLocationName] = useState(
    hostelPlace?.locationName || '',
  );
  const [contact, setContact] = useState(hostelPlace?.contact || user?.name);
  const [phoneNumber, setPhoneNumber] = useState(
    hostelPlace?.phoneNumber || user?.phone,
  );

  const _renderRight = useMemo(() => {
    return (
      <RightIcon
        onPress={() => _handleDelete({ locationId: hostelPlace?._id || '' })}
      />
    );
  }, [hostelPlace]);

  useEffect(() => {
    if (isEditRoom) {
      setLocationId(hostelPlace?._id);
      navigation.setOptions({
        headerRight: () => _renderRight,
        title: t('UPDATE_ADDRESS'),
      });
    }
  }, [isEditRoom, hostelPlace, _renderRight, navigation, t]);

  // To validate fully of hostel informations
  const isValidateNewHostel = useMemo(() => {
    return locationName && hostelPlace?.address && contact && phoneNumber;
  }, [contact, hostelPlace?.address, locationName, phoneNumber]);

  const _onDeleteLocation = debounce(async (params: { locationId: string }) => {
    onDeleteRoomHousekeepingLocation(params, {
      onSuccess: () => {
        getUser();
        navigation.canGoBack() && navigation.goBack();
      },
    });
  }, 300);

  const _handleDelete = (params: { locationId: string }) => {
    Alert.alert.open({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('CONFIRM_DELETE'),
      actions: [
        {
          text: t('BTN_BACK'),
          style: 'cancel',
        },
        {
          text: t('CONFIRM'),
          onPress: async () => {
            Alert.alert.close();
            _onDeleteLocation(params);
          },
        },
      ],
    });
  };

  const _onSubmitSaveLocation = async () => {
    // call api create
    if (isEditRoom) {
      // call api update
      onUpdateRoomHousekeepingLocation(
        {
          ...hostelPlace,
          locationName,
          locationId,
          contact,
          phoneNumber,
        },
        {
          onSuccess: () => {
            getUser();
            navigation.canGoBack() && navigation.goBack();
          },
        },
      );
      return;
    }

    onCreateHousekeepingLocation(
      {
        ...hostelPlace,
        locationName,
        contact,
        phoneNumber,
      },
      {
        onSuccess: () => {
          getUser();
          navigation.pop(2);
        },
      },
    );
  };

  return (
    <BlockView
      flex
      backgroundColor={ColorsV2.neutralWhite}
    >
      <KeyboardAware
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <RenderAddress
          isShowFooter
          isEditRoom={isEditRoom}
          name={hostelPlace?.shortAddress}
          description={hostelPlace?.address}
        />
        <BlockView margin={{ horizontal: Spacing.SPACE_16 }}>
          <CTextInput
            value={locationName}
            containerStyle={styles.inputContainerStyle}
            labelStyle={styles.labelStyle}
            inputContainerStyle={styles.inputContainerStyle}
            label={t('NAME_OF_LOCATION')}
            onChangeText={(value) => setLocationName(value)}
            placeholder={t('CREATE_LOCATION_PLACE_HOLDER_NAME_LOCATION')}
            testID="txtLocationName"
          />
          <CTextInput
            testID={'txtContactName'}
            value={contact}
            containerStyle={styles.inputContainerStyle}
            labelStyle={styles.labelStyle}
            label={t('CONTACT_NAME')}
            onChangeText={(value) => setContact(value)}
            placeholder={t('CREATE_LOCATION_PLACE_HOLDER_CONTACT_NAME')}
            inputContainerStyle={styles.inputContainerStyle}
          />
          <CTextInput
            testID={'txtPhoneNumber'}
            value={phoneNumber}
            keyboardType="numeric"
            containerStyle={styles.inputContainerStyle}
            labelStyle={styles.labelStyle}
            label={t('PHONE_NUMBER')}
            onChangeText={(value) => setPhoneNumber(value)}
            placeholder={t('CREATE_LOCATION_PLACE_HOLDER_PHONE_NUMBER')}
            inputContainerStyle={styles.inputContainerStyle}
          />
        </BlockView>
      </KeyboardAware>
      <BlockView
        inset={['bottom']}
        style={styles.boxFooter}
      >
        <PrimaryButton
          onPress={_onSubmitSaveLocation}
          title={t('CONTINUE')}
          disabled={!isValidateNewHostel}
          testID="btnSaveLocation"
        />
      </BlockView>
    </BlockView>
  );
};
