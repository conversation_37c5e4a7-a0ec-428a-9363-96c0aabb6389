import {
  AppStorage,
  createZustand,
  IAddons,
  IAddress,
  IDate,
  IOptionAreaOfficeCleaning,
  IPrice,
  IService,
  IUser,
  IVatInfo,
  Maybe,
  Requirement,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

interface AppState {
  address: IAddress;
  duration: number;
  requirements: Requirement[];
  isPremium: boolean;
  isAutoChooseTasker: boolean;
  isFavouriteTasker: boolean;
  gender: string;
  pet: any;
  addons: IAddons[];
  date: Maybe<IDate>;
  schedule: string[];
  isEnabledSchedule: boolean;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: Maybe<IPrice>;
  service: Maybe<IService>;
  paymentMethod: any;
  promotion: any;
  isLoadingPrice: boolean;
  loadingPostTask: boolean;
  homeNumber: string;
  weekdays: number[];
  isFirstOpen: boolean;
  detailOfficeCleaning: Maybe<IOptionAreaOfficeCleaning>;
  vatInfo: Maybe<IVatInfo>;
  isUpdateVatInfoToUser: boolean;
  forceTasker: Maybe<IUser>;
  dateOptions: Maybe<IDate[]>;

  // housekeeping state
  hostelPlace: Maybe<IAddress>;

  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setIsFavouriteTasker: (isFavouriteTasker: boolean) => void;
  setGender: (gender: string) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date?: IDate) => void;
  setSchedule: (schedule?: string[]) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price?: Maybe<IPrice>) => void;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod: any) => void;
  setPromotion: (promotion: any) => void;
  setLoadingPrice: (isLoadingPrice: boolean) => void;
  setService: (service: IService) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setWeekdays: (weekdays?: number[]) => void;
  setIsFirstOpen: () => void;
  setDetailOfficeCleaning: (detail: IOptionAreaOfficeCleaning) => void;
  setVatInfo: (vatInfo: IVatInfo) => void;
  setIsUpdateVatInfoToUser: (isUpdateVatInfoToUser: boolean) => void;
  setForceTasker: (forceTasker: Maybe<IUser>) => void;
  setDateOptions: (dateOptions: IDate[]) => void;
  setDate: (date: IDate, service?: Maybe<IService>) => void;

  // housekeeping state
  setHostelPlace: (hostelPlace: Maybe<IAddress>) => void;
  resetHousekeepingState: () => void;
  resetState: () => void;
}

export const usePostTaskStore = createZustand<AppState>()(
  persist(
    (set, get) => ({
      address: {},
      duration: 0,
      requirements: [],
      isPremium: false,
      isAutoChooseTasker: true,
      isFavouriteTasker: false,
      gender: '',
      pet: '',
      addons: [],
      date: null,
      schedule: [],
      isEnabledSchedule: false,
      note: '',
      isApplyNoteForAllTask: false,
      homeNumber: '',
      price: null,
      service: null,
      paymentMethod: null,
      promotion: null,
      isLoadingPrice: false,
      loadingPostTask: false,
      relatedTask: null,
      weekdays: [],
      isFirstOpen: true,
      detailOfficeCleaning: null,
      vatInfo: null,
      isUpdateVatInfoToUser: false,
      forceTasker: null,
      dateOptions: null,

      // housekeeping state
      hostelPlace: null,

      setLoadingPostTask: (loadingPostTask: boolean) =>
        set({ loadingPostTask: loadingPostTask }),
      setAddress: (address: IAddress) => set({ address: address }),
      setDuration: (duration: number) => set({ duration: duration }),
      setRequirements: (requirements: Requirement[]) =>
        set({ requirements: requirements }),
      setIsPremium: (isPremium: boolean) => set({ isPremium: isPremium }),
      setIsAutoChooseTasker: (isAutoChooseTasker: boolean) =>
        set({ isAutoChooseTasker: isAutoChooseTasker }),
      setIsFavouriteTasker: (isFavouriteTasker: boolean) =>
        set({ isFavouriteTasker: isFavouriteTasker }),
      setGender: (gender: string) => set({ gender: gender }),
      setAddons: (addons: IAddons[]) => set({ addons: addons }),
      setPet: (pet: any) => set({ pet: pet }),
      setDateTime: (date?: IDate) => set({ date: date }),
      setSchedule: (schedule?: string[]) => set({ schedule: schedule }),
      setNote: (note: string) => set({ note: note }),
      setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
        set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
      setPrice: (price?: Maybe<IPrice>) => set({ price: price }),
      setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
      setPaymentMethod: (paymentMethod: any) =>
        set({ paymentMethod: paymentMethod }),
      setPromotion: (promotion: any) => set({ promotion: promotion }),
      setLoadingPrice: (isLoadingPrice: boolean) =>
        set({ isLoadingPrice: isLoadingPrice }),
      setService: (service: IService) => set({ service: service }),
      setWeekdays: (weekdays?: number[]) => set({ weekdays: weekdays }),
      setIsFirstOpen: () => set({ isFirstOpen: false }),
      setDetailOfficeCleaning: (detail: IOptionAreaOfficeCleaning) =>
        set({ detailOfficeCleaning: detail }),
      setVatInfo: (vatInfo: IVatInfo) => set({ vatInfo: vatInfo }),
      setIsUpdateVatInfoToUser: (isUpdateVatInfoToUser: boolean) =>
        set({ isUpdateVatInfoToUser: isUpdateVatInfoToUser }),
      setForceTasker: (forceTasker: Maybe<IUser>) =>
        set({ forceTasker: forceTasker }),
      setDateOptions: (dateOptions: IDate[]) =>
        set({ dateOptions: dateOptions }),
      setDate: (date: IDate) => set({ date: date }),

      setHostelPlace: (hostelPlace: Maybe<IAddress>) =>
        set({ hostelPlace: hostelPlace }),
      resetHousekeepingState: () =>
        set({
          price: null,
        }),
      resetState: () =>
        set({
          address: {},
          duration: 0,
          requirements: [],
          isPremium: false,
          isAutoChooseTasker: true,
          isFavouriteTasker: false,
          gender: '',
          pet: '',
          addons: [],
          date: null,
          schedule: [],
          isEnabledSchedule: false,
          note: '',
          isApplyNoteForAllTask: false,
          homeNumber: '',
          price: null,
          service: null,
          paymentMethod: null,
          promotion: null,
          isLoadingPrice: false,
          loadingPostTask: false,
          weekdays: [],
          isFirstOpen: true,
          detailOfficeCleaning: null,
          vatInfo: null,
          isUpdateVatInfoToUser: false,
        }),
    }),
    {
      name: 'housekeeping-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      partialize: (state) => ({ isFirstOpen: state.isFirstOpen }),
    },
  ),
);
