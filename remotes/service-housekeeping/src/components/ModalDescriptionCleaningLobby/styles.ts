import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_16,
    overflow: 'hidden',
  },
  backgroundImage: {
    height: 200,
    backgroundColor: ColorsV2.neutral100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backgroundIcon: {
    width: 200,
    height: 200,
    opacity: 0.8,
  },
  content: {
    padding: Spacing.SPACE_24,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: Spacing.SPACE_16,
    right: Spacing.SPACE_16,
    zIndex: 1,
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_FULL,
    padding: Spacing.SPACE_08,
  },
});
