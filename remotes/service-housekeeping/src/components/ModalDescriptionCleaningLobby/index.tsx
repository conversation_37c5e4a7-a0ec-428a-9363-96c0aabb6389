import React from 'react';
import { TouchableOpacity } from 'react-native';
import {
  BlockView,
  ColorsV2,
  CText,
  FontSizes,
  getTextWithLocale,
  IconAssets,
  IconImage,
  IHouseKeepingOption,
  Spacing,
} from '@btaskee/design-system';

import { styles } from './styles';

interface IModalContentProps {
  handleClose: () => void;
  option?: IHouseKeepingOption;
}

export const ModalDescriptionCleaningLobby = ({
  option,
  handleClose,
}: IModalContentProps) => {
  return (
    <BlockView style={styles.container}>
      <BlockView style={styles.backgroundImage}>
        <IconImage
          source={{ uri: option?.icon }}
          size={200}
          style={styles.backgroundIcon}
        />
      </BlockView>

      <BlockView style={styles.content}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleClose}
        >
          <IconImage
            source={IconAssets.icCloseModal}
            color={ColorsV2.neutral800}
            size={24}
          />
        </TouchableOpacity>

        <CText
          bold
          size={FontSizes.SIZE_18}
          center
          margin={{ bottom: Spacing.SPACE_16 }}
        >
          {getTextWithLocale(option?.text)}
        </CText>

        <CText
          center
          color={ColorsV2.neutral600}
          size={FontSizes.SIZE_14}
        >
          {getTextWithLocale(option?.description)}
        </CText>
      </BlockView>
    </BlockView>
  );
};
