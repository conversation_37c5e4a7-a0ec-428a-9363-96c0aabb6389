import React, { useRef } from 'react';
import { TouchableOpacity } from 'react-native';
import {
  BlockView,
  CModal,
  CModalHandle,
  ColorsV2,
  CText,
  FontSizes,
  getTextWithLocale,
  IconAssets,
  IconImage,
  IHouseKeepingOption,
  Spacing,
  Switch,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { ModalDescriptionCleaningLobby } from '../ModalDescriptionCleaningLobby';
import { styles } from './styles';

interface ICleaningLobbyProps {
  options?: IHouseKeepingOption[];
}

export const CleaningLobby = ({ options }: ICleaningLobbyProps) => {
  const { t } = useI18n();
  const modalRef = useRef<CModalHandle>();

  const { options: selectedOptions, setOptions } = usePostTaskStore();

  const option = options?.find(
    (item: IHouseKeepingOption) => item?.name === 'CLEANING_LOBBY',
  );
  const selectedCleaningLobby = selectedOptions?.find(
    (item: IHouseKeepingOption) => item?.name === 'CLEANING_LOBBY',
  );

  const onPress = () => {
    const updatedOptions = [...selectedOptions];
    const existingIndex = updatedOptions.findIndex(
      (item) => item.name === option?.name,
    );

    if (existingIndex >= 0) {
      // Remove if already selected
      updatedOptions.splice(existingIndex, 1);
    } else if (option) {
      // Add if not selected
      updatedOptions.push({
        name: option.name,
        text: option.text,
      });
    }

    setOptions(updatedOptions);
  };

  const handleViewMore = () => {
    modalRef?.current?.open && modalRef?.current?.open();
  };

  const onCloseModal = () => {
    modalRef?.current?.close && modalRef?.current?.close();
  };

  if (!option) {
    return null;
  }

  return (
    <BlockView>
      <BlockView
        row
        jBetween
        margin={{ top: Spacing.SPACE_12 }}
      >
        <BlockView
          flex
          row
          margin={{ right: Spacing.SPACE_12 }}
        >
          <IconImage
            source={{ uri: option?.icon }}
            size={24}
            style={styles.iconImage}
          />
          <BlockView
            flex
            margin={{ horizontal: Spacing.SPACE_12 }}
          >
            <BlockView
              flex
              row
              horizontal
            >
              <CText
                margin={{ right: Spacing.SPACE_08 }}
                size={FontSizes.SIZE_14}
              >
                {getTextWithLocale(option?.text)}
              </CText>
              <TouchableOpacity
                onPress={handleViewMore}
                activeOpacity={0.7}
              >
                <IconImage
                  source={IconAssets.icQuestion}
                  color={ColorsV2.orange500}
                  size={16}
                />
              </TouchableOpacity>
            </BlockView>
            <CText
              color={ColorsV2.orange500}
              margin={{ top: Spacing.SPACE_04 }}
              size={FontSizes.SIZE_12}
            >
              {getTextWithLocale(option?.description)}
            </CText>
          </BlockView>
        </BlockView>
        <BlockView>
          <Switch
            value={Boolean(selectedCleaningLobby)}
            onValueChange={onPress}
          />
        </BlockView>
      </BlockView>
      <CModal
        ref={modalRef}
        hideButtonClose
        containerModal={{ maxHeight: Math.round(400) }}
        contentContainerStyle={styles.containerModal}
      >
        <ModalDescriptionCleaningLobby
          option={option}
          handleClose={onCloseModal}
        />
      </CModal>
    </BlockView>
  );
};
