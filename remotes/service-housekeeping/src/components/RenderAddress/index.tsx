import React from 'react';
import { ImageProps, TextStyle, TouchableOpacity } from 'react-native';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  IconAssets,
  IconImage,
  Spacing,
} from '@btaskee/design-system';

import { useAppNavigation, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';

interface IRenderAddressProps {
  name?: string;
  description?: string;
  isShowFooter?: boolean;
  ComponentRightButton?: React.JSX.Element;
  icon?: ImageProps['source'];
  descriptionStyle?: TextStyle;
  isEditRoom?: boolean;
}

export const RenderAddress = ({
  name,
  description,
  ComponentRightButton,
  isShowFooter,
  icon,
  descriptionStyle,
  isEditRoom,
}: IRenderAddressProps) => {
  const { t } = useI18n();
  const navigation = useAppNavigation();

  const onPress = () => {
    if (isEditRoom) {
      // For edit room flow, replace current screen to prevent stack duplication
      navigation.replace(RouteName.ChooseAddress, { isEditRoom: true });
      return;
    }
    // For new location flow, go back to previous ChooseAddress screen
    return navigation.goBack();
  };

  return (
    <BlockView margin={Spacing.SPACE_16}>
      <BlockView
        row
        padding={Spacing.SPACE_16}
        border={{ width: 1, color: ColorsV2.neutral100 }}
        radius={BorderRadius.RADIUS_08}
      >
        <IconImage
          source={icon || IconAssets.icLocation}
          color={ColorsV2.orange500}
          size={24}
        />
        <BlockView
          flex
          margin={{ left: Spacing.SPACE_12 }}
        >
          <BlockView row>
            <CText
              flex
              bold
              size={FontSizes.SIZE_16}
              padding={{ right: Spacing.SPACE_08 }}
            >
              {name}
            </CText>
            <ConditionView
              condition={Boolean(ComponentRightButton)}
              viewTrue={ComponentRightButton}
            />
          </BlockView>
          <CText
            margin={{ top: Spacing.SPACE_08 }}
            style={descriptionStyle}
          >
            {description}
          </CText>
        </BlockView>
      </BlockView>
      <ConditionView
        condition={Boolean(isShowFooter)}
        viewTrue={
          <TouchableOpacity
            testID="btnChooseOtherAddress"
            onPress={onPress}
          >
            <BlockView
              center
              row
              horizontal
              margin={{ top: Spacing.SPACE_12 }}
            >
              <IconImage
                source={IconAssets.icPlusCycle}
                color={ColorsV2.green500}
                size={24}
              />
              <CText
                margin={{ left: Spacing.SPACE_08 }}
                size={FontSizes.SIZE_16}
                color={ColorsV2.green500}
              >
                {t('CHOOSE_OTHER_ADDRESS')}
              </CText>
            </BlockView>
          </TouchableOpacity>
        }
      />
    </BlockView>
  );
};
