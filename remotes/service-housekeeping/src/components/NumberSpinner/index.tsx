import React, { memo, useEffect, useState } from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FontSizes,
  IconAssets,
  IconImage,
  Spacing,
  TouchableOpacity,
  AnimationHelper,
} from '@btaskee/design-system';

const MAX_VALUE = 10;

interface INumberSpinnerProps {
  amount?: number;
  testID?: string;
  data?: any;
  onPress: (value: any) => void;
  size?: number;
  maxValue?: number;
  callBack?: () => void;
}

export const NumberSpinner = memo(
  ({ amount = 0, maxValue = MAX_VALUE, testID, data, onPress, size, callBack }: INumberSpinnerProps) => {
    const [number, setNumber] = useState(amount);

    useEffect(() => {
      setNumber(amount);
    }, [amount]);

    const _decrement = () => {
      if (number < 0) {
        return;
      }
      AnimationHelper.runLayoutAnimation();
      setNumber(number - 1);
      if (data) {
        onPress({ name: data?.name, text: data?.text, description: data?.description, quantity: number - 1 });
      } else {
        onPress(number - 1);
      }
      callBack && callBack();
    };

    const _increment = () => {
      if (number >= maxValue) {
        return;
      }
      AnimationHelper.runLayoutAnimation();
      setNumber(number + 1);
      if (data) {
        onPress({ name: data?.name, text: data?.text, description: data?.description, quantity: number + 1 });
      } else {
        onPress(number + 1);
      }
    };

    const isMax = Boolean(maxValue && number >= maxValue);
    const isMin = Boolean(number < 1);

    return (
      <BlockView
        row
        horizontal
        jBetween
        backgroundColor={ColorsV2.neutral100}
        padding={Spacing.SPACE_08}
        radius={BorderRadius.RADIUS_08}
      >
        <TouchableOpacity
          disabled={isMin}
          testID={`btnMinus_${testID}`}
          onPress={_decrement}
        >
          <BlockView
            backgroundColor={isMin ? ColorsV2.neutral200 : ColorsV2.neutralWhite}
            radius={BorderRadius.RADIUS_04}
            padding={Spacing.SPACE_04}
          >
            <IconImage
              source={IconAssets.icMinus}
              color={isMin ? ColorsV2.neutralWhite : ColorsV2.neutral800}
              size={size ? size : 24}
            />
          </BlockView>
        </TouchableOpacity>

        <CText testID={'txtAmount'} bold size={FontSizes.SIZE_16}>
          {number}
        </CText>

        <TouchableOpacity testID={`btnIncrement_${testID}`} disabled={isMax} onPress={_increment}>
          <BlockView
            backgroundColor={isMax ? ColorsV2.neutral200 : ColorsV2.neutralWhite}
            radius={BorderRadius.RADIUS_04}
            padding={Spacing.SPACE_04}
          >
            <IconImage
              source={IconAssets.icPlus}
              color={isMax ? ColorsV2.neutralWhite : ColorsV2.neutral800}
              size={size ? size : 24}
            />
          </BlockView>
        </TouchableOpacity>
      </BlockView>
    );
  },
);
