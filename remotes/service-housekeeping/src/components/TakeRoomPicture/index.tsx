import React, { useMemo } from 'react';
import { isEmpty } from 'lodash';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  getTextWithLocale,
  Spacing,
} from '@btaskee/design-system';
import { IHouseKeepingOption, IRoomType } from '@btaskee/design-system';
import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';
import { NumberSpinner } from '../NumberSpinner';
import { styles } from './styles';

interface ITakeRoomPictureProps {
  options?: IHouseKeepingOption[];
}

export const TakeRoomPicture = ({ options }: ITakeRoomPictureProps) => {
  const { t } = useI18n();
  const {
    rooms,
    options: selectedOptions,
    setOptions
  } = usePostTaskStore();

  const totalQuantity = useMemo(() => {
    return rooms.reduce((sum: number, room: IRoomType) => sum + (room?.quantity || 0), 0);
  }, [rooms]);

  const optionsRoomNeedToSetUp = options?.find(
    (item: IHouseKeepingOption) => item?.name === 'SETUP_ROOM'
  );

  const currentSetupRoom = selectedOptions?.find(
    (item: IHouseKeepingOption) => item?.name === 'SETUP_ROOM'
  );

  const handleSetupRoomChange = (value: number) => {
    let updatedOptions = [...selectedOptions];
    const existingIndex = updatedOptions.findIndex(item => item.name === 'SETUP_ROOM');

    if (value === 0) {
      // Remove if quantity is 0
      if (existingIndex >= 0) {
        updatedOptions.splice(existingIndex, 1);
      }
    } else if (optionsRoomNeedToSetUp) {
      const setupRoomOption = {
        name: optionsRoomNeedToSetUp.name,
        text: optionsRoomNeedToSetUp.text,
        quantity: value,
      };

      if (existingIndex >= 0) {
        // Update existing
        updatedOptions[existingIndex] = setupRoomOption;
      } else {
        // Add new
        updatedOptions.push(setupRoomOption);
      }
    }

    setOptions(updatedOptions);
  };

  return (
    <BlockView style={styles.container}>
      <BlockView>
        {rooms?.map((item: IRoomType) => {
          return (
            <BlockView
              key={item?.name}
              style={styles.roomItem}
            >
              <CText size={FontSizes.SIZE_14}>
                {getTextWithLocale(item.text)}
              </CText>
              <CText
                color={ColorsV2.neutral600}
                size={FontSizes.SIZE_12}
                margin={{ top: Spacing.SPACE_04 }}
              >
                {t('ROOM_PICTURE_PLACEHOLDER')}
              </CText>
            </BlockView>
          );
        })}
      </BlockView>

      <ConditionView
        condition={!isEmpty(optionsRoomNeedToSetUp)}
        viewTrue={
          <BlockView row jBetween>
            <CText bold style={styles.left}>
              {t('NUMER_ROOM_NEED_TO_SET_UP')}
            </CText>
            <BlockView style={styles.right}>
              <NumberSpinner
                amount={currentSetupRoom?.quantity || 0}
                onPress={handleSetupRoomChange}
                maxValue={totalQuantity}
                size={16}
              />
            </BlockView>
          </BlockView>
        }
      />
    </BlockView>
  );
};
