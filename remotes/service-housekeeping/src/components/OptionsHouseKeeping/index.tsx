import React from 'react';
import {
  BlockView,
  CText,
  FontSizes,
  IHouseKeepingOption,
  Spacing,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { CleaningLobby } from '../CleaningLobby';

interface IOptionsHouseKeepingProps {
  options?: IHouseKeepingOption[];
}

export const OptionsHouseKeeping = ({ options }: IOptionsHouseKeepingProps) => {
  const { t } = useI18n();

  if (isEmpty(options)) {
    return null;
  }

  return (
    <BlockView margin={{ top: Spacing.SPACE_16 }}>
      <CText
        bold
        size={FontSizes.SIZE_18}
      >
        {t('OPTIONAL')}
      </CText>
      <CleaningLobby options={options} />
    </BlockView>
  );
};
