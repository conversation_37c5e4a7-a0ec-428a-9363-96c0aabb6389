import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FontSizes,
  Spacing,
  TouchableOpacity,
  getTextWithLocale,
  IconImage,
} from '@btaskee/design-system';
import { DeviceHelper } from '@btaskee/design-system';
import { IHostelType } from '@btaskee/design-system';
import { useI18n } from '@hooks';
import { styles } from './styles';

interface IHostelItemProps {
  icon?: string;
  name?: string;
  isActive?: boolean;
  onPress: () => void;
  testID?: string;
}

const HostelItem = ({
  icon,
  name,
  isActive,
  onPress,
  testID,
}: IHostelItemProps) => {
  return (
    <TouchableOpacity testID={testID} onPress={onPress}>
      <BlockView
        flex
        align="center"
        padding={{ vertical: Spacing.SPACE_16 }}
        width={Math.round(DeviceHelper.WINDOW.WIDTH * 0.31 - 32)}
        radius={BorderRadius.RADIUS_08}
        border={{ 
          width: 1, 
          color: isActive ? ColorsV2.orange500 : ColorsV2.neutral100 
        }}
      >
        <IconImage
          source={{ uri: icon }}
          size={Math.round(DeviceHelper.WINDOW.WIDTH * 0.2 - 32)}
        />
        <CText
          flex
          center
          bold={isActive}
          margin={{ 
            top: Spacing.SPACE_08, 
            horizontal: Spacing.SPACE_08 
          }}
          color={isActive ? ColorsV2.orange500 : ColorsV2.neutral800}
        >
          {name}
        </CText>
      </BlockView>
    </TouchableOpacity>
  );
};

interface IHostelTypeProps {
  hostelTypes?: IHostelType[];
  typeHostelSelected?: IHostelType | null;
  onChangeHostelType: (type: IHostelType) => void;
}

export const HostelType = ({ 
  hostelTypes, 
  typeHostelSelected, 
  onChangeHostelType 
}: IHostelTypeProps) => {
  const { t } = useI18n();

  return (
    <BlockView style={styles.container}>
      <CText bold size={FontSizes.SIZE_20}>
        {t('HOUSE_KEEPING.CHOOSE_HOME_TYPE')}
      </CText>
      <CText style={styles.description}>
        {t('HOUSE_KEEPING.CONTENT_CHOOSE_HOME_TYPE')}
      </CText>
      <BlockView style={styles.itemsContainer}>
        {hostelTypes?.map((item, index) => {
          const isActive = Boolean(item?.name === typeHostelSelected?.name);
          return (
            <HostelItem
              key={index}
              icon={item?.icon}
              name={getTextWithLocale(item?.text)}
              isActive={isActive}
              onPress={() => onChangeHostelType(item)}
              testID={`btnHostelType_${item?.name}`}
            />
          );
        })}
      </BlockView>
    </BlockView>
  );
};
