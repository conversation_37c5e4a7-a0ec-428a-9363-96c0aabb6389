import React from 'react';
import {
  <PERSON>View,
  BorderRadius,
  ColorsV2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ont<PERSON><PERSON>,
  getTextWithLocale,
  IconImage,
  IHostelType,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface IHostelItemProps {
  icon?: string;
  name?: string;
  isActive?: boolean;
  onPress: () => void;
  testID?: string;
}

const WIDTH_SLIDE = DeviceHelper.WINDOW.WIDTH - 32;

const HostelItem = ({
  icon,
  name,
  isActive,
  onPress,
  testID,
}: IHostelItemProps) => {
  return (
    <TouchableOpacity
      testID={testID}
      onPress={onPress}
    >
      <BlockView
        flex
        align="center"
        padding={{ vertical: Spacing.SPACE_16 }}
        width={Math.round(WIDTH_SLIDE * 0.31)}
        radius={BorderRadius.RADIUS_08}
        border={{
          width: 1,
          color: isActive ? ColorsV2.orange500 : ColorsV2.neutral100,
        }}
      >
        <IconImage
          source={{ uri: icon }}
          size={Math.round(WIDTH_SLIDE * 0.2)}
        />
        <CText
          flex
          center
          bold={isActive}
          margin={{
            top: Spacing.SPACE_08,
            horizontal: Spacing.SPACE_08,
          }}
          color={isActive ? ColorsV2.orange500 : ColorsV2.neutral800}
        >
          {name}
        </CText>
      </BlockView>
    </TouchableOpacity>
  );
};

interface IHostelTypeProps {
  hostelTypes?: IHostelType[];
  typeHostelSelected?: IHostelType | null;
  onChangeHostelType: (type: IHostelType) => void;
}

export const HostelType = ({
  hostelTypes,
  typeHostelSelected,
  onChangeHostelType,
}: IHostelTypeProps) => {
  const { t } = useI18n();

  return (
    <BlockView style={styles.container}>
      <CText
        bold
        size={FontSizes.SIZE_20}
      >
        {t('CHOOSE_HOME_TYPE')}
      </CText>
      <CText style={styles.description}>{t('CONTENT_CHOOSE_HOME_TYPE')}</CText>
      <BlockView style={styles.itemsContainer}>
        {hostelTypes?.map((item, index) => {
          const isActive = Boolean(item?.name === typeHostelSelected?.name);
          return (
            <HostelItem
              key={index}
              icon={item?.icon}
              name={getTextWithLocale(item?.text)}
              isActive={isActive}
              onPress={() => onChangeHostelType(item)}
              testID={`btnHostelType_${item?.name}`}
            />
          );
        })}
      </BlockView>
    </BlockView>
  );
};
