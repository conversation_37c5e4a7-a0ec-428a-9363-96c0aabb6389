import React from 'react';
import { TouchableOpacity } from 'react-native';
import {
  ColorsV2,
  CText,
  FontSizes,
  IconAssets,
  IconImage,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useAppNavigation, useI18n } from '@hooks';

import { styles } from './styles';

export const ProcessButton = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();

  const onPress = () => {
    // For now, we'll navigate to a placeholder or show an alert
    // In the original, this navigated to HousekeepingWorkingProcess
    // navigation.navigate(RouteName.HousekeepingWorkingProcess);
    console.log('Navigate to working process');
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
    >
      <IconImage
        source={IconAssets.icProcess}
        size={32}
        color={ColorsV2.neutral800}
      />
      <SizedBox width={Spacing.SPACE_16} />
      <CText
        flex
        size={FontSizes.SIZE_14}
        color={ColorsV2.neutral800}
      >
        {t('TASK_DETAIL')}
      </CText>
      <IconImage
        source={IconAssets.icArrowRight}
        size={16}
        color={ColorsV2.neutral600}
      />
    </TouchableOpacity>
  );
};
