export enum RouteName {
  Home = 'Housekeeping/Home',
  IntroService = 'Housekeeping/IntroService',
  ChooseAddress = 'Housekeeping/ChooseAddress',
  ChooseService = 'Housekeeping/ChooseService',
  ChooseDateTime = 'Housekeeping/ChooseDateTime',
  ConfirmAndPayment = 'Housekeeping/ConfirmAndPayment',
  PostTaskSuccess = 'Housekeeping/PostTaskSuccess',
  ChooseHostel = 'Housekeeping/ChooseHostel',
  CreateNewHostel = 'Housekeeping/CreateNewHostel',
  WorkingProcess = 'Housekeeping/WorkingProcess',
}
