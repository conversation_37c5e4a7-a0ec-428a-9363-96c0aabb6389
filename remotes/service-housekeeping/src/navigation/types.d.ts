import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RouteName } from './RouteName';

export type RootStackParamList = {
  [RouteName.Home]: undefined;
  [RouteName.IntroService]: undefined;
  [RouteName.ChooseAddress]: { isEditRoom?: boolean } | undefined;
  [RouteName.ChooseService]: undefined;
  [RouteName.ChooseDateTime]: undefined;
  [RouteName.ConfirmAndPayment]: undefined;
  [RouteName.PostTaskSuccess]: undefined;
  [RouteName.ChooseDuration]: undefined;
  [RouteName.ChooseHostel]: {
    isHideCurrentLocation?: boolean;
  };
  [RouteName.CreateNewHostel]:
    | {
        data?: any;
        isEditRoom?: boolean;
      }
    | undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> =
  NativeStackNavigationProp<RootStackParamList, T>;
