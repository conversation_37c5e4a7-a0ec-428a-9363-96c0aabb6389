const fs = require('fs');

const files = [
  { name: 'English', path: './en.json' },
  { name: 'Indonesian', path: './id.json' },
  { name: 'Korean', path: './ko.json' },
  { name: 'Malaysian', path: './ms.json' },
  { name: 'Thai', path: './th.json' },
  { name: 'Vietnamese', path: './vi.json' },
];

const keysToExtract = [
  "ADD_NEW_LOCATION",
  "CHOOSE_ADDRESS",
  "NAME_OF_LOCATION",
  "CREATE_LOCATION_PLACE_HOLDER_NAME_LOCATION",
  "CONTACT_NAME",
  "CREATE_LOCATION_PLACE_HOLDER_CONTACT_NAME",
  "PHONE_NUMBER",
  "CREATE_LOCATION_PLACE_HOLDER_PHONE_NUMBER"
];

files.forEach((file) => {
  try {
    const raw = fs.readFileSync(file.path, 'utf-8');
    const json = JSON.parse(raw);
    const result = {};

    keysToExtract.forEach((key) => {
      if (key.includes('.')) {
        const parts = key.split('.');
        let objectText = json[parts[0]];
        parts.forEach((part, index) => {
          if (objectText[part] && index !== 0) {
            objectText = objectText[part];
          }
        });
        result[key] = objectText;
      }

      if (json[key]) {
        result[key] = json[key];
      }
    });

    console.log(`\n \n ============== Results for ${file.name} ==============`);
    Object.entries(result).forEach(([key, value]) => {
      console.log(`"${key}": ${JSON.stringify(value)},`);
    });
  } catch (error) {
    console.error(`Error processing ${file.name} file:`, error.message);
  }
});
